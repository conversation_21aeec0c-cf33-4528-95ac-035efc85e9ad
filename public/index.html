<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据查询系统 - 游客访问</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .top-nav {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-logo {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-logo i {
            color: #409eff;
            font-size: 24px;
        }

        .login-btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 24px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .login-btn:hover {
            background: #337ecc;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        /* 开发控制面板 */
        .dev-controls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #856404;
            display: none;
        }

        .dev-controls.show {
            display: block;
        }

        .dev-controls label {
            margin-left: 8px;
            cursor: pointer;
        }

        /* 主要内容区域 */
        .query-page {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 搜索容器样式 */
        .search-container {
            background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
        }

        .search-header {
            padding: 24px 24px 0;
            color: white;
        }

        .search-title {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .title-icon {
            font-size: 28px;
        }

        .search-subtitle {
            margin: 0 0 20px 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .search-form {
            background: rgba(255, 255, 255, 0.95);
            margin: 0 24px 24px;
            padding: 24px;
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-item {
            flex: 1;
            min-width: 280px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #606266;
            font-size: 14px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
            color: #606266;
        }

        .search-input:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .search-input::placeholder {
            color: #c0c4cc;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
        }

        .search-btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 32px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .search-btn:hover {
            background: #337ecc;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .reset-btn {
            background: #f5f7fa;
            color: #909399;
            border: 1px solid #dcdfe6;
            padding: 12px 32px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .reset-btn:hover {
            background: #ecf5ff;
            color: #409eff;
            border-color: #b3d8ff;
        }

        /* 结果容器样式 */
        .result-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .result-stats {
            padding: 20px 24px;
            background: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-icon {
            color: #409eff;
            font-size: 18px;
        }

        .stats-text {
            color: #606266;
            font-size: 14px;
        }

        .stats-text strong {
            color: #409eff;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f5f7fa;
            color: #909399;
            font-weight: 500;
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #ebeef5;
            color: #606266;
            font-size: 14px;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .tag-info {
            background: #ecf5ff;
            color: #409eff;
            border: 1px solid #b3d8ff;
        }

        .tag-warning {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #f5dab1;
        }

        .tag-success {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #c2e7b0;
        }

        /* 空状态样式 */
        .empty-state, .welcome-state {
            padding: 60px 20px;
            text-align: center;
        }

        .welcome-content {
            max-width: 500px;
            margin: 0 auto;
        }

        .welcome-title {
            font-size: 20px;
            color: #303133;
            margin: 16px 0 12px;
            font-weight: 600;
        }

        .welcome-text {
            font-size: 14px;
            color: #909399;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .welcome-tips {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 32px;
        }

        .tip-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            color: #606266;
        }

        .tip-icon {
            color: #409eff;
            font-size: 16px;
        }

        /* 加载状态 */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #909399;
        }

        .loading.show {
            display: block;
        }

        .loading i {
            color: #409eff;
            font-size: 24px;
            margin-bottom: 12px;
        }

        /* 隐藏状态 */
        .hidden {
            display: none;
        }

        /* 空状态图标颜色 */
        .empty-state > div:first-child,
        .welcome-state > div:first-child {
            color: #c0c4cc;
        }

        .empty-state p:first-of-type {
            color: #303133;
            font-weight: 500;
        }

        .empty-state p:last-of-type {
            color: #909399;
        }

        /* 表格内图标样式 */
        .data-table .fas {
            color: #409eff;
        }

        .data-table .fa-clock {
            color: #909399;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .query-page {
                padding: 12px;
            }

            .nav-container {
                padding: 0 12px;
            }

            .form-row {
                flex-direction: column;
            }

            .form-item {
                min-width: auto;
            }

            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .search-btn, .reset-btn {
                width: 100%;
            }

            .search-header {
                padding: 20px 20px 0;
            }

            .search-form {
                margin: 0 20px 20px;
                padding: 20px;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
<!-- 顶部导航栏 -->
<nav class="top-nav">
    <div class="nav-container">
        <div class="nav-logo">
            <i class="fas fa-database"></i>
            数据查询系统
        </div>
        <button class="login-btn" onclick="handleLogin()">
            <i class="fas fa-sign-in-alt"></i>
            登录
        </button>
    </div>
</nav>

<!-- 主要内容区域 -->
<div class="query-page">
    <!-- 搜索工作栏 -->
    <div class="search-container">
        <div class="search-header">
            <h3 class="search-title">
                <i class="fas fa-search title-icon"></i>
                数据查询
            </h3>
            <p class="search-subtitle">请输入查询条件进行精确搜索</p>
        </div>

        <form class="search-form" onsubmit="handleQuery(event)">
            <div class="form-row">
                <div class="form-item">
                    <label class="form-label">查询结果</label>
                    <input
                            type="text"
                            id="searchInput"
                            class="search-input"
                            placeholder="请输入精确结果进行查询"
                    >
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    搜索查询
                </button>
                <button type="button" class="reset-btn" onclick="resetQuery()">
                    <i class="fas fa-redo"></i>
                    重置条件
                </button>
            </div>
        </form>
    </div>

    <!-- 结果展示区域 -->
    <div class="result-container">
        <!-- 结果统计 -->
        <div id="resultStats" class="result-stats hidden">
            <i class="fas fa-file-alt stats-icon"></i>
            <span class="stats-text">共找到 <strong id="totalCount">0</strong> 条结果</span>
        </div>

        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <div>正在查询数据...</div>
        </div>

        <!-- 数据表格 -->
        <div id="tableContainer" class="hidden">
            <table class="data-table">
                <thead>
                <tr>
                    <th>编号</th>
                    <th>数据类型</th>
                    <th>查询结果</th>
                    <th>创建时间</th>
                </tr>
                </thead>
                <tbody id="tableBody">
                <!-- 数据将通过JavaScript动态插入 -->
                </tbody>
            </table>
        </div>

        <!-- 空状态展示 -->
        <div id="emptyState" class="empty-state hidden">
            <div style="font-size: 48px; margin-bottom: 16px;">
                <i class="fas fa-search"></i>
            </div>
            <p style="font-size: 16px; margin: 16px 0 8px;">未找到匹配的查询结果</p>
            <p style="font-size: 14px; margin-bottom: 24px;">请尝试调整查询条件后重新搜索</p>
            <button class="search-btn" onclick="resetQuery()">重新查询</button>
        </div>

        <!-- 初始状态 -->
        <div id="welcomeState" class="welcome-state">
            <div style="font-size: 64px; margin-bottom: 16px;">
                <i class="fas fa-database"></i>
            </div>
            <div class="welcome-content">
                <h4 class="welcome-title">欢迎使用数据查询系统</h4>
                <p class="welcome-text">请在上方输入查询条件，开始您的数据探索之旅</p>
                <div class="welcome-tips">
                    <div class="tip-item">
                        <i class="fas fa-info-circle tip-icon"></i>
                        <span>支持精确结果匹配查询</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // API配置
    const API_CONFIG = {
        baseURL: '/admin-api',
        timeout: 30000,
        // 开发模式：如果API不可用，使用模拟数据
        useMockData: false
    };

    // 模拟数据（作为备选）
    const MOCK_DATA = [
        {
            id: 1,
            type: 1,
            value: '示例查询结果1',
            batchId: 1001,
            userId: 2001,
            createTime: '2024-01-15T10:30:00'
        },
        {
            id: 2,
            type: 2,
            value: '示例查询结果2',
            batchId: 1002,
            userId: 2002,
            createTime: '2024-01-15T11:45:00'
        },
        {
            id: 3,
            type: 1,
            value: 'test数据',
            batchId: 1003,
            userId: 2003,
            createTime: '2024-01-15T14:20:00'
        }
    ];

    // 数据类型映射
    const DATA_TYPE_MAP = {
        1: '手机号',
        2: 'UPI',
        3: '银行卡',
        4: '其他数据'
    };

    let hasSearched = false;

    // API请求函数
    async function apiRequest(url, options = {}) {
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // 添加可能需要的CORS头
                'Access-Control-Allow-Origin': '*',
                ...options.headers
            },
            // 添加CORS模式
            mode: 'cors',
            credentials: 'omit',
            ...options
        };

        try {
            const response = await fetch(`${API_CONFIG.baseURL}${url}`, config);

            if (!response.ok) {
                // 提供更详细的错误信息
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
            }

            const data = await response.json();

            // 检查业务状态码 (通常是0表示成功，200也可能表示成功)
            if (data.code !== 0 && data.code !== 200) {
                throw new Error(data.msg || data.message || '请求失败');
            }

            return data.data || data;
        } catch (error) {
            console.error('API请求失败:', error);
            // 如果是网络错误，提供更友好的提示
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查服务器是否启动');
            }
            throw error;
        }
    }

    // 格式化时间
    function formatDateTime(dateTime) {
        if (!dateTime) return '';
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 处理搜索
    async function handleQuery(event) {
        if (event) {
            event.preventDefault();
        }

        const searchValue = document.getElementById('searchInput').value.trim();
        if (!searchValue) {
            alert('请输入要查询的结果');
            return;
        }

        // 显示加载状态
        showLoading();

        try {
            let results = [];

            if (API_CONFIG.useMockData) {
                // 使用模拟数据
                results = MOCK_DATA.filter(item =>
                    item.value && item.value.includes(searchValue)
                );
                displayResults(results);
                hasSearched = true;
                return;
            }

            // 首先尝试游客专用API
            try {
                const params = new URLSearchParams({
                    value: searchValue
                });

                const data = await apiRequest(`/system/data-info/ano-page?${params}`);
                results = Array.isArray(data) ? data : (data.list || []);
                displayResults(results);
                hasSearched = true;
            } catch (guestApiError) {

            }
        } catch (error) {
            console.error('所有API都失败:', error);

            // 如果所有API都失败，提供模拟数据作为最后备选
            if (confirm('API连接失败，是否使用演示数据？\n\n' + error.message)) {
                const results = MOCK_DATA.filter(item =>
                    item.value && item.value.includes(searchValue)
                );
                displayResults(results);
            } else {
                displayResults([]);
            }
            hasSearched = true;
        }
    }

    // 重置查询
    function resetQuery() {
        document.getElementById('searchInput').value = '';
        hideAllStates();
        document.getElementById('welcomeState').classList.remove('hidden');
        hasSearched = false;
    }

    // 显示加载状态
    function showLoading() {
        hideAllStates();
        document.getElementById('loading').classList.add('show');
    }

    // 显示结果
    function displayResults(results) {
        hideAllStates();

        if (results.length === 0) {
            document.getElementById('emptyState').classList.remove('hidden');
        } else {
            document.getElementById('resultStats').classList.remove('hidden');
            document.getElementById('tableContainer').classList.remove('hidden');
            document.getElementById('totalCount').textContent = results.length;

            // 填充表格数据
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            results.forEach(item => {
                const row = document.createElement('tr');
                // 处理数据类型显示
                const typeText = DATA_TYPE_MAP[item.type] || '未知类型';
                // 格式化时间
                const formattedTime = formatDateTime(item.createTime);

                row.innerHTML = `
                        <td><span class="tag tag-info">${item.id}</span></td>
                        <td>${typeText}</td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-copy" style="color: #6366f1;"></i>
                                <span style="font-weight: 500;">${item.value || ''}</span>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 6px; font-size: 13px; color: #6b7280;">
                                <i class="fas fa-clock"></i>
                                <span>${formattedTime}</span>
                            </div>
                        </td>
                    `;
                tbody.appendChild(row);
            });
        }
    }

    // 隐藏所有状态
    function hideAllStates() {
        document.getElementById('loading').classList.remove('show');
        document.getElementById('resultStats').classList.add('hidden');
        document.getElementById('tableContainer').classList.add('hidden');
        document.getElementById('emptyState').classList.add('hidden');
        document.getElementById('welcomeState').classList.add('hidden');
    }

    // 处理登录
    function handleLogin() {
        // 跳转到系统登录页面
        window.location.href = '/admin/';
    }


    // 显示/隐藏开发控制面板（双击logo显示）
    function toggleDevControls() {
        const devControls = document.getElementById('devControls');
        devControls.classList.toggle('show');
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 双击logo显示开发控制面板
        document.querySelector('.nav-logo').addEventListener('dblclick', toggleDevControls);

        // 检查是否在本地开发环境
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            document.getElementById('devControls').classList.add('show');
        }
    });

    // 回车键搜索
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleQuery();
        }
    });
</script>
</body>
</html>
