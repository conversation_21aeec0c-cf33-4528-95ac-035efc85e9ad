import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 批次信息信息 */
export interface DataBatch {
          id: number; // 批次ID
          remark: string; // 备注
          userId?: number; // 所属用户
          batchCount: number; // 批次数量
  }

// 批次信息 API
export const DataBatchApi = {
  // 查询批次信息分页
  getDataBatchPage: async (params: any) => {
    return await request.get({ url: `/system/data-batch/page`, params })
  },

  // 查询批次信息详情
  getDataBatch: async (id: number) => {
    return await request.get({ url: `/system/data-batch/get?id=` + id })
  },

  // 新增批次信息
  createDataBatch: async (data: DataBatch) => {
    return await request.post({ url: `/system/data-batch/create`, data })
  },

  // 修改批次信息
  updateDataBatch: async (data: DataBatch) => {
    return await request.put({ url: `/system/data-batch/update`, data })
  },

  // 删除批次信息
  deleteDataBatch: async (id: number) => {
    return await request.delete({ url: `/system/data-batch/delete?id=` + id })
  },

  /** 批量删除批次信息 */
  deleteDataBatchList: async (ids: number[]) => {
    return await request.delete({ url: `/system/data-batch/delete-list?ids=${ids.join(',')}` })
  },

  // 导出批次信息 Excel
  exportDataBatch: async (params) => {
    return await request.download({ url: `/system/data-batch/export-excel`, params })
  },
}
