import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** IP黑名单信息 */
export interface IpBlacklist {
          id: number; // ID
          ip?: string; // ip黑名单
          remark: string; // 备注
  }

// IP黑名单 API
export const IpBlacklistApi = {
  // 查询IP黑名单分页
  getIpBlacklistPage: async (params: any) => {
    return await request.get({ url: `/system/ip-blacklist/page`, params })
  },

  // 查询IP黑名单详情
  getIpBlacklist: async (id: number) => {
    return await request.get({ url: `/system/ip-blacklist/get?id=` + id })
  },

  // 新增IP黑名单
  createIpBlacklist: async (data: IpBlacklist) => {
    return await request.post({ url: `/system/ip-blacklist/create`, data })
  },

  // 修改IP黑名单
  updateIpBlacklist: async (data: IpBlacklist) => {
    return await request.put({ url: `/system/ip-blacklist/update`, data })
  },

  // 删除IP黑名单
  deleteIpBlacklist: async (id: number) => {
    return await request.delete({ url: `/system/ip-blacklist/delete?id=` + id })
  },

  /** 批量删除IP黑名单 */
  deleteIpBlacklistList: async (ids: number[]) => {
    return await request.delete({ url: `/system/ip-blacklist/delete-list?ids=${ids.join(',')}` })
  },

  // 导出IP黑名单 Excel
  exportIpBlacklist: async (params) => {
    return await request.download({ url: `/system/ip-blacklist/export-excel`, params })
  },
}
