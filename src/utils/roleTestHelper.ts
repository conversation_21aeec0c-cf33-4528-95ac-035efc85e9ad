/**
 * 角色权限测试辅助工具
 * 用于测试不同角色用户的页面访问权限
 */

import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { checkAdminRole } from '@/utils/permission'

const { wsCache } = useCache()

/**
 * 模拟设置用户角色（仅用于测试）
 * @param roles 角色数组
 */
export function mockUserRoles(roles: string[]) {
  const mockUserInfo = {
    id: 1,
    avatar: '',
    nickname: 'Test User',
    deptId: 1,
    roles: roles,
    permissions: ['*:*:*']
  }
  
  wsCache.set(CACHE_KEY.USER, mockUserInfo)
  console.log('🔧 Mock user roles set:', roles)
}

/**
 * 测试不同角色的权限
 */
export function testRolePermissions() {
  console.log('🧪 开始测试角色权限...')
  
  // 测试普通用户
  console.log('\n1. 测试普通用户权限:')
  mockUserRoles(['user'])
  console.log('   - 是否有管理员权限:', checkAdminRole())
  
  // 测试admin角色
  console.log('\n2. 测试admin角色权限:')
  mockUserRoles(['admin'])
  console.log('   - 是否有管理员权限:', checkAdminRole())
  
  // 测试super_admin角色
  console.log('\n3. 测试super_admin角色权限:')
  mockUserRoles(['super_admin'])
  console.log('   - 是否有管理员权限:', checkAdminRole())
  
  // 测试多角色用户
  console.log('\n4. 测试多角色用户权限:')
  mockUserRoles(['user', 'admin'])
  console.log('   - 是否有管理员权限:', checkAdminRole())
  
  // 测试无角色用户
  console.log('\n5. 测试无角色用户权限:')
  mockUserRoles([])
  console.log('   - 是否有管理员权限:', checkAdminRole())
  
  console.log('\n✅ 角色权限测试完成')
}

/**
 * 获取当前用户角色信息
 */
export function getCurrentUserRoles() {
  const userInfo = wsCache.get(CACHE_KEY.USER)
  return {
    roles: userInfo?.roles || [],
    hasAdminRole: checkAdminRole()
  }
}

/**
 * 清除用户信息缓存
 */
export function clearUserCache() {
  wsCache.delete(CACHE_KEY.USER)
  console.log('🗑️ 用户缓存已清除')
}
