<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户登录"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="login-dialog"
    @close="handleClose"
  >
    <div class="login-form-container">
      <div class="login-header">
        <Icon icon="ep:user" class="login-icon" />
        <h3 class="login-title">欢迎登录</h3>
        <p class="login-subtitle">请输入您的账号信息</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        label-width="0"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            :disabled="loading"
          />
        </el-form-item>

        <!-- 验证码 -->
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe" :disabled="loading">
              记住我
            </el-checkbox>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            <span v-if="!loading">登录</span>
            <span v-else>登录中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'login-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const message = useMessage()
const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 登录表单
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const captchaEnable = ref(false)
const captchaImg = ref('')

const loginForm = reactive({
  username: '',
  password: '',
  code: '',
  uuid: '',
  rememberMe: false,
  captchaVerification: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 30, message: '用户名长度在 2 到 30 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 5, max: 20, message: '密码长度在 5 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度在 4 到 6 个字符', trigger: 'blur' }
  ]
}

/** 获取验证码 */
const getCaptcha = async () => {
  try {
    const data = await LoginApi.getCode({
      captchaType: 'arithmetic'
    })

    // 根据实际API返回结构调整
    if (data.repCode === '0000' || data.code === 0) {
      const repData = data.repData || data.data || data
      captchaEnable.value = true
      captchaImg.value = 'data:image/png;base64,' + (repData.originalImageBase64 || repData.img)
      loginForm.uuid = repData.token || repData.uuid
    } else {
      captchaEnable.value = false
      console.log('验证码未启用或获取失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    captchaEnable.value = false
  }
}

/** 处理登录 */
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 构建登录参数
    const loginData = {
      username: loginForm.username,
      password: loginForm.password,
      captchaVerification: captchaEnable.value
        ? loginForm.uuid + '---' + loginForm.code
        : ''
    }

    // 调用登录API
    const tokenData = await LoginApi.login(loginData)
    
    // 保存token
    authUtil.setToken(tokenData)
    
    // 记住我功能
    if (loginForm.rememberMe) {
      authUtil.setLoginForm(loginForm)
    } else {
      authUtil.removeLoginForm()
    }

    // 获取用户信息
    await userStore.setUserInfoAction()
    
    // 生成路由
    await permissionStore.generateRoutes()

    // 触发登录成功事件
    emit('login-success')
    
    message.success('登录成功')

  } catch (error) {
    console.error('登录失败:', error)
    message.error('登录失败，请检查用户名和密码')
    
    // 刷新验证码
    if (captchaEnable.value) {
      getCaptcha()
      loginForm.code = ''
    }
  } finally {
    loading.value = false
  }
}

/** 处理弹窗关闭 */
const handleClose = () => {
  // 重置表单
  loginFormRef.value?.resetFields()
  loginForm.code = ''
  loginForm.uuid = ''
  captchaImg.value = ''
}

/** 获取缓存的登录表单 */
const getLoginFormCache = () => {
  const loginFormCache = authUtil.getLoginForm()
  if (loginFormCache) {
    loginForm.username = loginFormCache.username || ''
    loginForm.password = loginFormCache.password || ''
    loginForm.rememberMe = loginFormCache.rememberMe || false
  }
}

// 监听弹窗打开，初始化数据
watch(dialogVisible, (visible) => {
  if (visible) {
    getLoginFormCache()
    getCaptcha()
  }
})
</script>

<style scoped>
.login-dialog {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0;
  text-align: center;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.login-form-container {
  padding: 0;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.login-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.login-subtitle {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #8c8c8c;
}

.login-form {
  width: 100%;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  transition: all 0.3s;
}

.captcha-image:hover {
  border-color: #1890ff;
  background: #e6f7ff;
}

.captcha-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 3px;
}

.captcha-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }
  
  .login-title {
    font-size: 20px;
  }
  
  .captcha-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .captcha-image {
    width: 100%;
    height: 44px;
  }
}
</style>
