<template>
  <div class="query-page">
    <!-- 搜索工作栏 -->
    <ContentWrap class="search-container">
      <div class="search-header">
        <h3 class="search-title">
          <Icon icon="ep:search" class="title-icon" />
          数据查询
        </h3>
        <p class="search-subtitle">请输入查询条件进行精确搜索</p>
      </div>

      <el-form
        class="search-form"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="80px"
      >
        <div class="form-row">
          <el-form-item label="查询结果" prop="value" class="form-item-custom">
            <el-input
              v-model="queryParams.value"
              placeholder="请输入精确结果进行查询"
              clearable
              @keyup.enter="handleQuery"
              class="search-input"
              prefix-icon="Search"
            />
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button type="primary" @click="handleQuery" class="search-btn">
            <Icon icon="ep:search" class="btn-icon" />
            搜索查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <Icon icon="ep:refresh" class="btn-icon" />
            重置查询
          </el-button>
        </div>
      </el-form>
    </ContentWrap>

    <!-- 结果展示区域 -->
    <ContentWrap class="result-container">
      <!-- 结果统计 -->
      <div v-if="hasSearched" class="result-stats">
        <div class="stats-item">
          <Icon icon="ep:document" class="stats-icon" />
          <span class="stats-text">共找到 <strong>{{ total }}</strong> 条结果</span>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        row-key="id"
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        class="data-table"
        element-loading-text="正在查询数据..."
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table-column label="编号" align="center" prop="id" width="180">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="数据类型" align="center" prop="type" min-width="150">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_DATA_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>

        <el-table-column label="查询结果" align="left" prop="value" min-width="180">
          <template #default="scope">
            <div class="result-cell">
              <Icon icon="ep:document-copy" class="result-icon" />
              <span class="result-text">{{ scope.row.value }}</span>
            </div>
          </template>
        </el-table-column>



        <el-table-column label="用户编号" align="center" prop="userId"  min-width="150" v-if="false">
          <template #default="scope">
            <el-tag type="success" size="small">{{ scope.row.userId }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          min-width="180"
        >
          <template #default="scope">
            <div class="time-cell">
              <Icon icon="ep:clock" class="time-icon" />
              <span>{{ dateFormatter(scope.row, null, scope.row.createTime) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="批次" align="center" prop="batchId" min-width="150" >
          <template #default="scope">
            <el-tag type="warning" size="small">{{ scope.row.batchId }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <!-- 空状态展示 -->
      <div v-if="!loading && list.length === 0 && hasSearched" class="empty-state">
        <el-empty
          description="未找到匹配的查询结果"
          :image-size="120"
        >
          <template #description>
            <p class="empty-text">未找到匹配的查询结果</p>
          </template>
          <el-button type="primary" @click="resetQuery">
            重新查询
          </el-button>
        </el-empty>
      </div>

      <!-- 初始状态 -->
      <div v-if="!loading && !hasSearched" class="welcome-state">
        <el-empty
          description="开始您的数据查询之旅"
          :image-size="130"
        >
          <template #description>
            <div class="welcome-content">
              <h4 class="welcome-title">欢迎使用数据查询系统</h4>
            </div>
          </template>
        </el-empty>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { DataInfoApi, DataInfo } from '@/api/system/datainfo'

/** 游客数据查询 列表 */
defineOptions({ name: 'GuestDataQuery' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<DataInfo[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const hasSearched = ref(false) // 是否已经搜索过
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  value: undefined, // 只保留结果查询，支持精确匹配
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 - 游客版本，只支持精确结果匹配 */
const getList = async () => {
  loading.value = true
  try {
    // 如果没有输入查询条件，不进行查询
    if (!queryParams.value && (!queryParams.createTime || queryParams.createTime.length === 0)) {
      list.value = []
      total.value = 0
      loading.value = false
      return
    }

    // 调用API进行精确查询
    const data = await DataInfoApi.getDataInfoPage(queryParams) // 暂时使用原API，后续可替换为游客专用API
    list.value = data.list
    total.value = data.total
    hasSearched.value = true
  } catch (error) {
    console.error('查询失败:', error)
    message.error('查询失败，请稍后重试')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  if (!queryParams.value?.trim()) {
    message.warning('请输入要查询的结果')
    return
  }
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  list.value = []
  total.value = 0
  hasSearched.value = false
}

/** 初始化 - 游客页面不自动加载数据 **/
onMounted(() => {
  // 游客页面不自动加载数据，需要用户主动搜索
})
</script>

<style scoped>
.query-page {
  background: #f4f6f9;
  min-height: calc(100vh - 80px); /* 改为 calc(100vh - 40px) */
  padding: 0px;
  overflow-y: auto; /* 添加这行 */
}

/* 搜索容器样式 - 若依蓝色风格 */
.search-container {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px; /* 改为 20px */
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}
.search-header {
  padding: 20px 24px 0; /* 改为 20px 24px 0 */
  color: white;
}

.search-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 22px;
}

.search-subtitle {
  margin: 0 0 16px 0;
  opacity: 0.9;
  font-size: 14px;
}

.search-form {
  background: rgba(255, 255, 255, 0.98);
  margin: 0 20px 20px; /* 改为 0 20px 20px */
  padding: 15px; /* 改为 15px */
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.form-item-custom {
  flex: 1;
  min-width: 280px;
}

.search-input {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.search-btn {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 4px;
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.reset-btn {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 4px;
  color: #595959;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.btn-icon {
  margin-right: 4px;
}

/* 结果容器样式 */
.result-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.result-stats {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  color: #1890ff;
  font-size: 16px;
}

.stats-text {
  color: #262626;
  font-size: 14px;
}

/* 表格样式 - 若依风格 */
.data-table {
  border-radius: 0;
}

:deep(.el-table) {
  --el-table-border-color: #e8e8e8;
  --el-table-bg-color: #ffffff;
  --el-table-tr-bg-color: #ffffff;
  --el-table-expanded-cell-bg-color: #ffffff;
}

:deep(.el-table th) {
  background: #fafafa;
  color: #262626;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  color: #595959;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafafa;
}

:deep(.el-table tr:hover > td) {
  background-color: #e6f7ff !important;
}

.result-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.result-icon {
  color: #1890ff;
  font-size: 14px;
}

.result-text {
  font-weight: 500;
  color: #262626;
}

.time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 13px;
  color: #8c8c8c;
}

.time-icon {
  font-size: 14px;
}

/* 分页样式 */
.pagination-container {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

:deep(.el-pagination) {
  --el-pagination-font-size: 13px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #595959;
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: #595959;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-disabled-color: #c0c4cc;
  --el-pagination-button-disabled-bg-color: #ffffff;
  --el-pagination-hover-color: #1890ff;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border: 1px solid #d9d9d9;
}

:deep(.el-pagination .el-pager li) {
  border: 1px solid #d9d9d9;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

/* 空状态样式 */
.empty-state, .welcome-state {
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #262626;
}

.empty-hint {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 20px;
}

.welcome-content {
  max-width: 480px;
  margin: 0 auto;
}

.welcome-title {
  font-size: 18px;
  color: #262626;
  margin: 16px 0 12px;
  font-weight: 600;
}

.welcome-text {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 24px;
  line-height: 1.6;
}

.welcome-tips {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 24px;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  color: #595959;
}

.tip-icon {
  color: #1890ff;
  font-size: 16px;
}

/* 标签样式优化 - 若依风格 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  height: 22px;
  line-height: 20px;
  padding: 0 7px;
}

:deep(.el-tag--info) {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
  color: #595959;
}

:deep(.el-tag--warning) {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #d46b08;
}

:deep(.el-tag--success) {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .query-page {
    padding: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-item-custom {
    min-width: auto;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-btn, .reset-btn {
    width: 100%;
  }

  .search-header {
    padding: 16px 20px 0;
  }

  .search-form {
    margin: 0 20px 20px;
    padding: 16px;
  }
}

/* 动画效果 */
.data-table {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  color: #1890ff;
}
</style>
