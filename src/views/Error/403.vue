<template>
  <div class="flex justify-center">
    <div class="text-center">
      <img src="@/assets/svgs/403.svg" alt="" width="350" />
      <div class="text-14px text-[var(--el-color-info)] mb-4">
        抱歉，您无权访问此页面。
      </div>
      <div class="text-12px text-[var(--el-color-warning)] mb-6">
        只有管理员（admin）或超级管理员（super_admin）角色才能访问后台管理页面。
      </div>
      <div class="mt-20px space-x-4">
        <ElButton type="primary" @click="goToFrontend">
          <Icon icon="ep:data-analysis" class="mr-1" />
          前往前台页面
        </ElButton>
        <ElButton @click="goHome">
          <Icon icon="ep:home-filled" class="mr-1" />
          返回首页
        </ElButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineOptions({ name: 'Error403' })

const { push } = useRouter()

const goToFrontend = () => {
  push('/frontend')
}

const goHome = () => {
  push('/')
}
</script>
