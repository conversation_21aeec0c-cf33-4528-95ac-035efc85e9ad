<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="批次" prop="remark">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入批次"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="所属用户" prop="userId" v-if="false">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入所属用户"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_BATCH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:data-batch:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['system:data-batch:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="danger"
          plain
          :disabled="isEmpty(checkedIds)"
          @click="handleDeleteBatch"
          v-hasPermi="['system:data-batch:delete']"
        >
          <Icon icon="ep:delete" class="mr-5px" /> 批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      row-key="id"
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleRowCheckboxChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="批次" align="center" prop="id" />
      <el-table-column label="数量" align="center" prop="batchCount" />
      <el-table-column label="所属用户" align="center" prop="userId" v-if="false" />
      <el-table-column label="失败数量" align="center" prop="failCount" v-if="false"/>
      <el-table-column
        label="时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_BATCH_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" min-width="160px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:data-batch:update']"
          >
            编辑
          </el-button>

          <!-- 下载按钮改为下拉菜单 -->
          <el-dropdown @command="(format) => handleDownloadBatch(scope.row.id, format)">
            <el-button
              link
              type="success"
              :loading="downloadLoading[scope.row.id]"
              v-hasPermi="['system:data-info:export']"
            >
              下载 <Icon icon="ep:arrow-down" class="ml-5px" />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="txt">
                  <Icon icon="ep:document" class="mr-5px" />
                  下载TXT
                </el-dropdown-item>
                <el-dropdown-item command="excel">
                  <Icon icon="ep:document" class="mr-5px" />
                  下载Excel
                </el-dropdown-item>
                <el-dropdown-item command="csv">
                  <Icon icon="ep:document" class="mr-5px" />
                  下载CSV
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:data-batch:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DataBatchForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { isEmpty } from '@/utils/is'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DataBatchApi, DataBatch } from '@/api/system/databatch'
import { DataInfoApi } from '@/api/system/datainfo'
import DataBatchForm from './DataBatchForm.vue'

/** 批次信息 列表 */
defineOptions({ name: 'DataBatch' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DataBatch[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  downloadFormat:'txt',
  remark: undefined,
  userId: undefined,
  status: undefined,
  createTime: [],
  id: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const downloadLoading = ref<Record<number, boolean>>({}) // 下载批次数据的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DataBatchApi.getDataBatchPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DataBatchApi.deleteDataBatch(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 批量删除批次信息 */
const handleDeleteBatch = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm('是否删除所选批次的所有数据')
    await DataBatchApi.deleteDataBatchList(checkedIds.value);
    message.success(t('common.delSuccess'))
    await getList();
  } catch {}
}

const checkedIds = ref<number[]>([])
const handleRowCheckboxChange = (records: DataBatch[]) => {
  checkedIds.value = records.map((item) => item.id);
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DataBatchApi.exportDataBatch(queryParams)
    download.excel(data, '批次信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 统一的下载批次信息操作 */
const handleDownloadBatch = async (batchId: number, format: string) => {
  try {
    // 设置当前批次的加载状态
    downloadLoading.value[batchId] = true

    // 调用API下载批次数据
    const data = await DataInfoApi.exportDataInfo({
      batchId,
      downloadFormat: format
    })

    // 根据格式设置文件扩展名
    const fileExtensions = {
      txt: '.txt',
      excel: '.xlsx',
      csv: '.csv'
    }

    const extension = fileExtensions[format] || '.txt'

    // 下载文件
    download.excel(data, `批次${batchId}信息${extension}`)
    message.success('下载成功')
  } catch (error) {
    message.error('下载失败')
  } finally {
    downloadLoading.value[batchId] = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
