<template>
  <Dialog v-model="dialogVisible" :title="`配置用户【${currentUser?.username}】的IP白名单`" width="700px">
    <div class="ip-whitelist-config">
      <div class="mb-4 flex justify-between items-center">
        <div>
          <el-button type="primary" @click="addIpInput">新增IP</el-button>
          <el-button type="danger" @click="clearAllIps" :disabled="ipList.length === 0">清空</el-button>
        </div>
        <div class="text-sm text-gray-600">
          共 {{ ipList.filter(ip => ip.trim()).length }} 个IP地址
        </div>
      </div>
      
      <div class="ip-list" v-if="ipList.length > 0">
        <div 
          v-for="(ip, index) in ipList" 
          :key="index" 
          class="ip-item flex items-center gap-2 mb-3 p-3 border border-gray-200 rounded-lg"
        >
          <div class="flex-1">
            <el-input
              v-model="ipList[index]"
              placeholder="请输入IP地址，如：*********** 或 ***********/24"
              :class="{ 'is-error': !validateIp(ipList[index]) && ipList[index] }"
              @blur="validateIpInput(index)"
            >
              <template #prepend>
                <span class="text-sm text-gray-500">IP {{ index + 1 }}</span>
              </template>
            </el-input>
            <div v-if="ipList[index] && !validateIp(ipList[index])" class="text-red-500 text-xs mt-1">
              IP地址格式不正确
            </div>
          </div>
          <el-button 
            type="danger" 
            size="small" 
            circle 
            @click="removeIp(index)"
            :icon="Delete"
            title="删除此IP"
          />
        </div>
      </div>
      
      <div v-else class="text-center text-gray-500 py-12 border-2 border-dashed border-gray-200 rounded-lg">
        <Icon icon="ep:plus" class="text-4xl mb-2" />
        <p>暂无IP白名单</p>
        <p class="text-sm">点击"新增IP"添加IP地址</p>
      </div>
      
      <div class="mt-6 p-4 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-800">
          <p class="font-semibold mb-2"><Icon icon="ep:info-filled" class="mr-1" />支持的IP格式：</p>
          <ul class="list-disc list-inside ml-2 space-y-1">
            <li><strong>单个IP地址：</strong>***********</li>
            <li><strong>IP段（CIDR）：</strong>***********/24</li>
            <li><strong>IP范围：</strong>***********-*************</li>
          </ul>
          <p class="mt-2 text-xs text-blue-600">
            * 留空表示允许所有IP访问，多个IP地址将以空格分隔保存
          </p>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="confirmConfig" :loading="saving">
        <Icon icon="ep:check" class="mr-1" />确 定
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { Delete } from '@element-plus/icons-vue'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'UserIpWhitelistDialog' })

const message = useMessage()

// 弹窗状态
const dialogVisible = ref(false)
const saving = ref(false)
const ipList = ref<string[]>([])
const currentUser = ref<UserApi.UserVO | null>(null)

// IP地址验证正则表达式
const IP_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
const CIDR_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/
const IP_RANGE_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

// 验证IP地址格式
const validateIp = (ip: string): boolean => {
  if (!ip || ip.trim() === '') return true // 空值允许，用户可能还在输入
  const trimmedIp = ip.trim()
  return IP_REGEX.test(trimmedIp) || CIDR_REGEX.test(trimmedIp) || IP_RANGE_REGEX.test(trimmedIp)
}

// 验证IP输入并显示错误提示
const validateIpInput = (index: number) => {
  const ip = ipList.value[index]
  if (ip && !validateIp(ip)) {
    message.error(`第${index + 1}个IP地址格式不正确`)
  }
}

// 新增IP输入框
const addIpInput = () => {
  ipList.value.push('')
}

// 移除IP
const removeIp = (index: number) => {
  ipList.value.splice(index, 1)
}

// 清空所有IP
const clearAllIps = () => {
  ipList.value = []
}

// 打开弹窗
const open = async (user: UserApi.UserVO) => {
  currentUser.value = user
  dialogVisible.value = true
  
  // 获取用户详细信息，包括IP白名单
  try {
    const userDetail = await UserApi.getUser(user.id)
    if (userDetail.iplist && userDetail.iplist.trim()) {
      ipList.value = userDetail.iplist.trim().split(/\s+/).filter(ip => ip)
    } else {
      ipList.value = []
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ipList.value = []
  }
  
  // 如果没有IP，默认添加一个空输入框
  if (ipList.value.length === 0) {
    addIpInput()
  }
}

// 确认配置
const emit = defineEmits(['success'])
const confirmConfig = async () => {
  if (!currentUser.value) return
  
  // 过滤掉空的IP地址
  const validIps = ipList.value.filter(ip => ip && ip.trim())
  
  // 验证所有IP地址格式
  const invalidIps = validIps.filter(ip => !validateIp(ip))
  if (invalidIps.length > 0) {
    message.error('存在格式不正确的IP地址，请检查后重试')
    return
  }
  
  // 检查重复IP
  const uniqueIps = [...new Set(validIps.map(ip => ip.trim()))]
  if (uniqueIps.length !== validIps.length) {
    message.warning('存在重复的IP地址，已自动去重')
  }
  
  try {
    saving.value = true
    
    // 更新用户IP白名单
    const updateData = {
      ...currentUser.value,
      iplist: uniqueIps.join(' ')
    }
    
    await UserApi.updateUser(updateData)
    message.success('IP白名单配置成功')
    
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('更新IP白名单失败:', error)
    message.error('配置失败，请重试')
  } finally {
    saving.value = false
  }
}

defineExpose({ open })
</script>

<style scoped>
.ip-whitelist-config {
  min-height: 300px;
}

.ip-item .el-input.is-error :deep(.el-input__wrapper) {
  border-color: var(--el-color-danger);
}

.ip-list {
  max-height: 400px;
  overflow-y: auto;
}
</style>
