<template>
  <div class="data-management">
    <!-- 主内容区域 -->
    <div class="main-content">
      <ContentWrap>
        <!-- 数据输入区域 -->
        <div class="data-input-section">
          <div class="input-header">
            <span class="file-selector">
              <input
                ref="fileInputRef"
                type="file"
                accept=".txt,.csv"
                @change="handleFileSelect"
                style="display: none"
              />
              <el-button @click="selectFile" size="small" :disabled="importing">选择文件</el-button>
            </span>
            <span class="reset-btn" @click="resetQuery" :disabled="importing">重置</span>
          </div>

          <div class="input-area">
            <el-input
              v-model="pasteData"
              type="textarea"
              :rows="6"
              placeholder="请粘贴数据列表&#10;每行一个数据超过10万建议使用文件方式导入数据&#10;文件大小不能超过20MB;点击左上方按钮上传或将数据文件直接拖到此处"
              class="data-textarea"
              @drop="handleDrop"
              @dragover="handleDragOver"
              @dragenter="handleDragEnter"
              @dragleave="handleDragLeave"
              :class="{ 'drag-over': isDragOver }"
              :readonly="isLargeFile"
              :disabled="fileLoading || importing"
            />
            <!-- 大文件提示 -->
            <div v-if="isLargeFile" class="large-file-tip">
              <i class="el-icon-info"></i>
              文件过大，已禁用预览。数据行数：{{ dataLineCount }}
            </div>
          </div>

          <div class="operator-info">
             <span v-if="fileLoading" class="loading-info" style="color: #409EFF;">
                 <i class="el-icon-loading"></i> 正在加载文件...
            </span>
            <span v-if="importing" class="importing-info" style="color: #E6A23C;">
                 <i class="el-icon-loading"></i> 正在入库中，请稍候...
            </span>
            <span v-if="currentFileName" class="file-info">
              当前文件：{{ currentFileName }} ({{ fileSize }})
            </span>
          </div>
        </div>

        <!-- 查询条件区域 -->
        <div class="query-section">
          <el-form
            :model="queryParams"
            ref="queryFormRef"
            :inline="true"
            class="query-form"
          >
            <el-form-item>
              <el-radio-group v-model="queryParams.type" @change="handleSearchTypeChange" :disabled="importing">
                <el-radio-button label="1">手机号</el-radio-button>
                <el-radio-button label="2">UPI</el-radio-button>
                <el-radio-button label="3">银行卡</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="handleQuery"
                :loading="loading"
                :disabled="importing"
              >
                {{ loading ? '查询中...' : '查询未入库数据' }}
              </el-button>
              <el-button
                type="primary"
                @click="handleQueryExist"
                :loading="existLoading"
                :disabled="importing"
              >
                {{ existLoading ? '查询中...' : '查询已入库数据' }}
              </el-button>
              <el-button
                type="success"
                @click="handleBatchImport"
                :loading="importing"
                :disabled="importing || !pasteData.trim()"
                v-hasPermi="['system:data-info:batch-import']"
              >
                <i v-if="importing" class="el-icon-loading"></i>
                {{ importing ? '入库中...' : '批量入库' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 提示信息 -->
        <div class="info-tip" v-if="!showResults">
          点击查询显示下面数据，不查询不显示
        </div>
      </ContentWrap>

      <!-- 查询结果区域 -->
      <ContentWrap v-if="showResults">
        <div class="results-section">
          <div class="results-header">
            <span class="section-title">
              {{queryParams.searchType=='exist'?'已入库数据':'不重复数据' }} (共 {{ total }} 条，格式正确 {{ validDataCount }} 条)
            </span>
            <div class="action-buttons">
              <el-button @click="copyResults" :disabled="importing">复制结果</el-button>
              <el-button @click="downloadTxt" :disabled="importing">下载txt</el-button>
              <el-button
                type="primary"
                @click="handleBatchImportResults"
                :loading="importing"
                v-if="queryParams.searchType=='notExist'"
                :disabled="importing || validDataCount === 0"
                v-hasPermi="['system:data-info:batch-import']"
              >
                <i v-if="importing" class="el-icon-loading"></i>
                {{ importing ? '入库中...' : '批量入库' }}
              </el-button>
            </div>
          </div>

          <div class="results-content">
            <el-table
              v-loading="loading"
              :data="displayList"
              :stripe="true"
              :show-overflow-tooltip="true"
              @selection-change="handleRowCheckboxChange"
              class="results-table"
            >
              <el-table-column label="数据" align="left" min-width="200">
                <template #default="{ row }">
                  {{ row.value }}
                </template>
              </el-table-column>
              <el-table-column label="格式" align="center" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="row.isValid ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ row.isValid ? '正确' : '错误' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :small="false"
                :disabled="importing"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </ContentWrap>
    </div>
  </div>

  <!-- 确认对话框 -->
  <el-dialog
    v-model="confirmDialogVisible"
    title="确认入库"
    width="450px"
    center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="confirm-content">
      <div class="confirm-icon">
        <i class="el-icon-question" style="color: #409EFF; font-size: 24px;"></i>
      </div>
      <div class="confirm-text">
        <p>确认将 <strong style="color: #409EFF;">{{ selectedCount }}</strong> 条数据入库？</p>
        <div v-if="invalidDataCount > 0" class="warning-text" style="color: #E6A23C; margin-top: 10px;">
          <i class="el-icon-warning"></i>
          注意：将跳过 {{ invalidDataCount }} 条格式错误的数据
        </div>
        <div v-if="importing" class="importing-progress" style="color: #67C23A; margin-top: 15px;">
          <i class="el-icon-loading"></i>
          正在处理中，请稍候...
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelImport" :disabled="importing">取消</el-button>
        <el-button
          v-hasPermi="['system:data-info:batch-import']"
          type="primary"
          @click="confirmImport"
          :loading="importing"
          :disabled="importing"
        >
          {{ importing ? '入库中...' : '确定入库' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 表单弹窗：添加/修改 -->
  <DataInfoForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import {DataInfo, DataInfoApi} from '@/api/system/datainfo'
import DataInfoForm from './DataInfoForm.vue'

/** 数据信息 列表 */
defineOptions({ name: 'DataInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 定义数据项接口
interface DataItem {
  value: string
  isValid: boolean
}

const isLargeFile = ref(false) // 是否为大文件
const dataLineCount = ref(0) // 数据行数
const rawFileContent = ref('') // 原始文件内容（不显示在textarea中）
const fileLoading = ref(false) // 文件加载状态
const loading = ref(true) // 列表的加载中
const importing  = ref(false) // 入库状态 - 新增
const existLoading = ref(false) // 入库状态 - 新增
const list = ref<DataItem[]>([]) // 完整的数据列表
const displayList = ref<DataItem[]>([]) // 当前页显示的数据
const total = ref(0) // 列表的总条数
const showResults = ref(false) // 是否显示查询结果
const pasteData = ref('') // 粘贴的数据
const confirmDialogVisible = ref(false) // 确认对话框
const selectedCount = ref(0) // 选中的数据条数
const validDataCount = ref(0) // 格式正确的数据条数
const invalidDataCount = ref(0) // 格式错误的数据条数

// 文件相关状态
const fileInputRef = ref<HTMLInputElement>()
const isDragOver = ref(false)
const currentFileName = ref('')
const fileSize = ref('')

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 修改查询参数，将 valueList 明确定义为 string[] 类型
const queryParams = reactive({
  type: '1', // 搜索类型：phone, upi, bankCard
  value: undefined,
  searchType: 'notExist',
  valueList: [] as string[],
})

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const checkedIds = ref<number[]>([])

/** 格式化文件大小 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/** 选择文件 */
const selectFile = () => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }
  fileInputRef.value?.click()
}

/** 处理文件选择 */
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    await readFile(file)
  }
}

/** 读取文件内容 */
const readFile = async (file: File) => {
  // 检查文件类型
  const allowedTypes = ['text/plain', 'text/csv', 'application/csv']
  const fileExtension = file.name.split('.').pop()?.toLowerCase()

  if (!allowedTypes.includes(file.type) && !['txt', 'csv'].includes(fileExtension || '')) {
    message.error('只支持 .txt 和 .csv 文件格式')
    return
  }

  // 检查文件大小（限制为20MB）
  const maxSize = 20 * 1024 * 1024 // 20MB
  if (file.size > maxSize) {
    message.error('文件大小不能超过20MB')
    return
  }

  fileLoading.value = true

  // 大文件阈值：1MB 或 10000 行
  const largeFileThreshold = 0.5 * 1024 * 1024 // 1MB

  try {
    if (file.size > largeFileThreshold) {
      // 大文件处理：分块读取
      await readLargeFile(file)
    } else {
      // 小文件正常处理
      await readSmallFile(file)
    }

    currentFileName.value = file.name
    fileSize.value = formatFileSize(file.size)
    message.success(`文件 "${file.name}" 加载成功`)
  } catch (error) {
    message.error('文件读取失败，请重试')
    console.error('文件读取错误:', error)
  } finally {
    fileLoading.value = false
  }
}

const readSmallFile = (file: File): Promise<void> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      pasteData.value = content
      rawFileContent.value = content
      isLargeFile.value = false
      dataLineCount.value = content.split('\n').filter(line => line.trim()).length
      resolve()
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'UTF-8')
  })
}

// 新增：大文件读取函数
const readLargeFile = (file: File): Promise<void> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      rawFileContent.value = content

      // 计算行数
      const lines = content.split('\n').filter(line => line.trim())
      dataLineCount.value = lines.length

      // 大文件不显示在textarea中，只显示提示信息
      pasteData.value = `已加载大文件：${file.name}\n数据行数：${dataLineCount.value} 行\n文件大小：${formatFileSize(file.size)}`
      isLargeFile.value = true

      resolve()
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'UTF-8')
  })
}

/** 处理拖拽进入 */
const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  if (!importing.value) {
    isDragOver.value = true
  }
}

/** 处理拖拽悬停 */
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
}

/** 处理拖拽离开 */
const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  // 检查是否真的离开了拖拽区域
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  const x = e.clientX
  const y = e.clientY

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
  }
}

/** 处理文件拖拽放置 */
const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]
    readFile(file)
  }
}

const PHONE_REGEX = /^\d{10,15}$/;
const UPI_REGEX = /@/;
const BANK_CARD_REGEX = /^\d{6,}$/;

/** 格式校验函数 */
const validateFormat = (value: string, type: string): boolean => {
  switch (type) {
    case '1':
      return PHONE_REGEX.test(value);
    case '2':
      return UPI_REGEX.test(value) && value.length > 0;
    case '3':
      return BANK_CARD_REGEX.test(value);
    default:
      return false;
  }
};

/** 处理数据并进行格式校验 */
const processDataWithValidation = async (dataList: string[], type: string): Promise<DataItem[]> => {
  return new Promise((resolve) => {
    const batchSize = 1000 // 每批处理1000条
    const result: DataItem[] = []
    let currentIndex = 0

    const processBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, dataList.length)

      for (let i = currentIndex; i < endIndex; i++) {
        result.push({
          value: dataList[i],
          isValid: validateFormat(dataList[i], type)
        })
      }

      currentIndex = endIndex

      if (currentIndex < dataList.length) {
        // 继续处理下一批
        setTimeout(processBatch, 0)
      } else {
        // 处理完成
        resolve(result)
      }
    }

    processBatch()
  })
}

/** 前端分页处理 */
const updateDisplayList = () => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  displayList.value = list.value.slice(start, end)
}

/** 查询列表 */
const getList = async () => {
  if(queryParams.searchType==='notExist'){
    loading.value = true
  }else{
    existLoading.value=true
  }

  try {
    const data = await DataInfoApi.getDataInfoBatch(queryParams)
    const rawList = data.list || data // 兼容不同的返回格式

    // 处理数据并进行格式校验
    list.value = await processDataWithValidation(rawList, queryParams.type)
    total.value = list.value.length

    // 统计格式正确和错误的数据
    validDataCount.value = list.value.filter(item => item.isValid).length
    invalidDataCount.value = list.value.filter(item => !item.isValid).length

    // 重置分页到第一页
    pagination.currentPage = 1
    updateDisplayList()
  } finally {
    loading.value = false
    existLoading.value=false
  }
}

/** 搜索类型改变 */
const handleSearchTypeChange = (value: string) => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  // 根据搜索类型设置对应的查询参数
  queryParams.type = value

  // 如果已有数据，重新进行格式校验
  if (list.value.length > 0) {
    list.value = list.value.map(item => ({
      ...item,
      isValid: validateFormat(item.value, value)
    }))

    // 重新统计
    validDataCount.value = list.value.filter(item => item.isValid).length
    invalidDataCount.value = list.value.filter(item => !item.isValid).length

    updateDisplayList()
  }
}

/** 处理粘贴数据，转换为字符串数组 */
const processPasteData = async (data: string): Promise<string[]> => {
  // 如果是大文件，使用原始内容
  const content = isLargeFile.value ? rawFileContent.value : data

  return new Promise((resolve) => {
    // 使用 setTimeout 分批处理，避免阻塞UI
    setTimeout(() => {
      const lines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
      resolve(lines)
    }, 0)
  })
}

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.searchType='notExist'
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  if (!pasteData.value.trim()) {
    message.warning('请先粘贴数据列表或选择文件')
    return
  }

  // 将粘贴的数据转换为字符串数组
  queryParams.valueList = await processPasteData(pasteData.value)
  showResults.value = true
  getList()
}

const handleQueryExist = async () => {
  queryParams.searchType='exist'
  if (!pasteData.value.trim()) {
    message.warning('请先粘贴数据列表或选择文件')
    return
  }
  existLoading.value = true
  // 将粘贴的数据转换为字符串数组
  queryParams.valueList = await processPasteData(pasteData.value)
  showResults.value = true
  await getList()
  existLoading.value = false
}

/** 重置按钮操作 */
const resetQuery = () => {
  if (importing.value) {
    message.warning('正在入库中，无法重置')
    return
  }

  pasteData.value = ''
  rawFileContent.value = ''
  isLargeFile.value = false
  dataLineCount.value = 0
  showResults.value = false
  queryParams.valueList = []
  list.value = []
  displayList.value = []
  total.value = 0
  validDataCount.value = 0
  invalidDataCount.value = 0
  pagination.currentPage = 1
  checkedIds.value = []
  currentFileName.value = ''
  fileSize.value = ''
  isDragOver.value = false

  // 清空文件输入
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }

  queryFormRef.value?.resetFields()
}

/** 分页大小改变 */
const handleSizeChange = (val: number) => {
  if (importing.value) return
  pagination.pageSize = val
  pagination.currentPage = 1 // 重置到第一页
  updateDisplayList()
}

/** 当前页改变 */
const handleCurrentChange = (val: number) => {
  if (importing.value) return
  pagination.currentPage = val
  updateDisplayList()
}

/** 批量导入操作 */
const handleBatchImport = async () => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  if (!pasteData.value.trim()) {
    message.warning('请先粘贴数据列表或选择文件')
    return
  }

  const dataList = await processPasteData(pasteData.value)
  selectedCount.value = dataList.length

  if (dataList.length === 0) {
    message.warning('没有数据可以入库')
    return
  }
  confirmDialogVisible.value = true
}

/** 批量导入结果数据 */
const handleBatchImportResults = () => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  if (list.value.length === 0) {
    message.warning('无不重复的数据')
    return
  }

  const validData = list.value.filter(item => item.isValid)
  const invalidData = list.value.filter(item => !item.isValid)

  selectedCount.value = validData.length
  invalidDataCount.value = invalidData.length

  if (validData.length === 0) {
    message.warning('没有格式正确的数据可以入库')
    return
  }

  confirmDialogVisible.value = true
}

/** 取消导入 */
const cancelImport = () => {
  if (importing.value) {
    message.warning('正在入库中，无法取消')
    return
  }
  confirmDialogVisible.value = false
}

/** 确认导入 */
const confirmImport = async () => {
  if (importing.value) {
    return
  }

  try {
    importing.value = true

    let dataList: string[] = []
    if (showResults.value) {
      // 从查询结果中只取格式正确的数据
      dataList = list.value
        .filter(item => item.isValid)
        .map(item => item.value)
    } else {
      // 从粘贴数据中只取格式正确的数据
      const rawDataList = await processPasteData(pasteData.value)
      const processedData = await processDataWithValidation(rawDataList, queryParams.type)
      dataList = processedData
        .filter(item => item.isValid)
        .map(item => item.value)
    }

    if (dataList.length === 0) {
      message.warning('没有格式正确的数据可以入库')
      return
    }

    // 调用批量导入API
    const response = await DataInfoApi.batchImport({
      type: queryParams.type,
      list: dataList // 只传递格式正确的数据
    })

    // 检查返回结果
    if (!response.batchId) {
      message.error(response.msg || '入库失败')
    } else {
      let successMsg = response.msg || '入库成功'
      if (invalidDataCount.value > 0) {
        successMsg += `，已跳过 ${invalidDataCount.value} 条格式错误的数据`
      }
      message.success(successMsg)

      // 入库成功后可以选择性重置数据
      // resetQuery()
    }

  } catch (error) {
    message.error('数据入库失败，请稍后重试')
    console.error('批量导入错误:', error)
  } finally {
    importing.value = false
    confirmDialogVisible.value = false
  }
}

/** 复制结果 */
const copyResults = () => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  // 复制完整查询结果到剪贴板
  const resultText = list.value.map(item => item.value).join('\n')
  navigator.clipboard.writeText(resultText)
  message.success('复制成功')
}

/** 下载txt */
const downloadTxt = () => {
  if (importing.value) {
    message.warning('正在入库中，请稍候...')
    return
  }

  // 下载完整查询结果
  const resultText = list.value.map(item => item.value).join('\n')
  const blob = new Blob([resultText], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = '查询结果.txt'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await DataInfoApi.deleteDataInfo(id)
    message.success(t('common.delSuccess'))

    // 从本地列表中移除已删除的项
    const index = list.value.findIndex(item => item.id === id)
    if (index > -1) {
      list.value.splice(index, 1)
      total.value = list.value.length

      // 重新统计
      validDataCount.value = list.value.filter(item => item.isValid).length
      invalidDataCount.value = list.value.filter(item => !item.isValid).length

      // 如果当前页没有数据了，回到上一页
      if (displayList.value.length === 1 && pagination.currentPage > 1) {
        pagination.currentPage--
      }

      updateDisplayList()
    }
  } catch {}
}

/** 行选择改变 */
const handleRowCheckboxChange = (records: DataItem[]) => {
  checkedIds.value = records.map((item) => item.id)
}

/** 初始化 **/
onMounted(() => {
  // 初始化时不自动加载数据，等用户查询后再显示
  loading.value = false
})
</script>

<style scoped>
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px 0;
}

.confirm-icon {
  flex-shrink: 0;
}

.confirm-text {
  flex: 1;
}

.confirm-text p {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.warning-text {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.importing-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.importing-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.data-textarea.drag-over {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.large-file-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f4f4f5;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.operator-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
  font-size: 14px;
}

.loading-info, .importing-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.file-info {
  color: #606266;
}

.query-form .el-form-item {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.info-tip {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}

/* 禁用状态样式 */
.el-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.el-radio-group.is-disabled .el-radio-button {
  opacity: 0.6;
}

.el-pagination.is-disabled {
  opacity: 0.6;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.el-icon-loading {
  animation: spin 1s linear infinite;
}

.data-textarea {
  transition: all 0.3s ease;
}

.data-textarea.drag-over :deep(.el-textarea__inner) {
  border-color: #409eff;
  background-color: #f0f9ff;
  border-style: dashed;
}

.file-info {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.operator-info {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.warning-text {
  font-size: 14px;
  margin-top: 10px;
}
.data-management {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 120px;
  background: #f5f5f5;
  border-right: 1px dashed #ccc;
  padding: 20px 0;
}

.nav-item {
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 1px dashed #ccc;
}

.nav-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.main-content {
  flex: 1;
  padding: 20px;
}

.data-input-section {
  border: 2px dashed #ccc;
  padding: 20px;
  margin-bottom: 20px;
}

.input-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.file-selector {
  background: #f0f0f0;
  padding: 5px 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.reset-btn {
  background: #f0f0f0;
  padding: 5px 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.data-textarea {
  margin-bottom: 10px;
}

.operator-info {
  color: #666;
  font-size: 12px;
}

.query-section {
  margin-bottom: 20px;
}

.query-form {
  display: flex;
  align-items: center;
  gap: 15px;
}

.query-tip {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 5px;
}

.info-tip {
  text-align: center;
  color: #ff4d4f;
  padding: 20px;
}

.results-section {
  border: 2px dashed #ccc;
  padding: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.results-content {
  margin-top: 20px;
}

.results-display {
  padding: 10px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  margin-bottom: 15px;
}

.results-table {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.confirm-content {
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

.dialog-footer {
  text-align: center;
}
</style>
