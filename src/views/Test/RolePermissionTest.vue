<template>
  <div class="role-test-page">
    <ContentWrap title="角色权限测试">
      <div class="test-container">
        <div class="current-status">
          <h3>当前用户状态</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户角色">
              <el-tag v-for="role in currentRoles" :key="role" class="mr-2">
                {{ role }}
              </el-tag>
              <span v-if="currentRoles.length === 0" class="text-gray-500">无角色</span>
            </el-descriptions-item>
            <el-descriptions-item label="管理员权限">
              <el-tag :type="hasAdminRole ? 'success' : 'danger'">
                {{ hasAdminRole ? '有权限' : '无权限' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="test-actions">
          <h3>测试操作</h3>
          <div class="action-buttons">
            <el-button type="primary" @click="runAllTests">
              运行所有测试
            </el-button>
            <el-button @click="refreshStatus">
              刷新状态
            </el-button>
            <el-button type="warning" @click="clearCache">
              清除缓存
            </el-button>
          </div>
          
          <div class="role-buttons">
            <h4>模拟不同角色:</h4>
            <el-button @click="setRole(['user'])" size="small">
              普通用户
            </el-button>
            <el-button @click="setRole(['admin'])" size="small" type="success">
              管理员
            </el-button>
            <el-button @click="setRole(['super_admin'])" size="small" type="primary">
              超级管理员
            </el-button>
            <el-button @click="setRole(['user', 'admin'])" size="small" type="info">
              多角色用户
            </el-button>
            <el-button @click="setRole([])" size="small" type="danger">
              无角色
            </el-button>
          </div>
        </div>

        <div class="page-access-test">
          <h3>页面访问测试</h3>
          <div class="access-buttons">
            <el-button @click="testPageAccess('/frontend')" type="success">
              测试前台页面
            </el-button>
            <el-button @click="testPageAccess('/')" type="primary">
              测试后台首页
            </el-button>
            <el-button @click="testPageAccess('/system/user')" type="warning">
              测试系统管理
            </el-button>
          </div>
        </div>

        <div class="test-results" v-if="testResults.length > 0">
          <h3>测试结果</h3>
          <div class="results-container">
            <div v-for="(result, index) in testResults" :key="index" class="result-item">
              <pre>{{ result }}</pre>
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { 
  testRolePermissions, 
  mockUserRoles, 
  getCurrentUserRoles, 
  clearUserCache 
} from '@/utils/roleTestHelper'

defineOptions({ name: 'RolePermissionTest' })

const router = useRouter()
const testResults = ref<string[]>([])
const currentRoles = ref<string[]>([])
const hasAdminRole = ref(false)

// 刷新当前状态
const refreshStatus = () => {
  const userInfo = getCurrentUserRoles()
  currentRoles.value = userInfo.roles
  hasAdminRole.value = userInfo.hasAdminRole
}

// 设置角色
const setRole = (roles: string[]) => {
  mockUserRoles(roles)
  refreshStatus()
}

// 运行所有测试
const runAllTests = () => {
  testResults.value = []
  
  // 捕获console.log输出
  const originalLog = console.log
  const logs: string[] = []
  
  console.log = (...args) => {
    logs.push(args.join(' '))
    originalLog(...args)
  }
  
  testRolePermissions()
  
  // 恢复console.log
  console.log = originalLog
  
  testResults.value = logs
  refreshStatus()
}

// 清除缓存
const clearCache = () => {
  clearUserCache()
  refreshStatus()
}

// 测试页面访问
const testPageAccess = (path: string) => {
  router.push(path).catch(err => {
    console.error('页面跳转失败:', err)
  })
}

// 初始化
onMounted(() => {
  refreshStatus()
})
</script>

<style scoped>
.role-test-page {
  padding: 20px;
}

.test-container > div {
  margin-bottom: 30px;
}

.action-buttons, .role-buttons, .access-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.role-buttons h4 {
  width: 100%;
  margin: 10px 0 5px 0;
}

.results-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.result-item {
  margin-bottom: 10px;
  font-family: monospace;
  font-size: 12px;
}

.result-item pre {
  margin: 0;
  white-space: pre-wrap;
}
</style>
